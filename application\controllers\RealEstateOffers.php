<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class RealEstateOffers extends MY_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Realestate_model');
        $this->load->library('upload');
        $this->load->helper('form');
        $this->load->library('form_validation');
    }

    public function index()
    {

        // Simple admin dashboard
        $data['ln'] = $this->session->userdata('ln');
		$data['ln'] = !empty($data['ln']) ? $data['ln'] : 'ar';
		$data['menu'] = 'Advertisements';
        $data['properties'] = $this->Realestate_model->get_properties(50, 0);
        $data['page_title'] = 'Admin Dashboard - Property Management';
        $data['page_class'] = 'admin-page';

        // Load views with template
         $this->load->view('admin/header', $data);
       $this->load->view('realestate/offerdashboard', $data);
        $this->load->view('admin/footer', $data);
    }

    public function show_property()
    {

        // Simple admin dashboard
        $data['ln'] = $this->session->userdata('ln');
		$data['ln'] = !empty($data['ln']) ? $data['ln'] : 'ar';
		$data['menu'] = 'Advertisements';
        $data['properties'] = $this->Realestate_model->get_properties(50, 0);
        $data['page_title'] = 'Admin Dashboard - Property Management';
        $data['page_class'] = 'admin-page';

        // Load views with template
         $this->load->view('admin/header', $data);
       $this->load->view('realestate/add_real_estate_offer', $data);
        $this->load->view('admin/footer', $data);
    }

    public function add_property()
    {
        // Set form validation rules
        $this->form_validation->set_rules('title_ar', 'Arabic Title', 'required|trim');
        $this->form_validation->set_rules('title_en', 'English Title', 'required|trim');
        $this->form_validation->set_rules('description_ar', 'Arabic Description', 'required|trim');
        $this->form_validation->set_rules('description_en', 'English Description', 'required|trim');
        $this->form_validation->set_rules('type', 'Property Type', 'required');
        $this->form_validation->set_rules('price', 'Price', 'required|numeric');
        $this->form_validation->set_rules('location_ar', 'Arabic Location', 'required|trim');
        $this->form_validation->set_rules('location_en', 'English Location', 'required|trim');

        if ($this->form_validation->run() == FALSE) {
            // Show form
            $data['page_title'] = 'Add New Property - Admin Panel';
            $data['page_class'] = 'admin-page add-property-page';

            // Load views with template
           $this->load->view('admin/header', $data);
            $this->load->view('realestate/add_real_estate_offer', $data);
            $this->load->view('admin/footer', $data);

        } else {
            // Handle multiple image uploads
            $upload_result = $this->upload_multiple_images();
            $main_image = $upload_result['main_image'];
            $gallery_images = $upload_result['gallery_images'];

            // Process form submission
            $property_data = array(
                'title_ar' => $this->input->post('title_ar'),
                'title_en' => $this->input->post('title_en'),
                'description_ar' => $this->input->post('description_ar'),
                'description_en' => $this->input->post('description_en'),
                'type' => $this->input->post('type'),
                'type_ar' => $this->get_type_arabic($this->input->post('type')),
                'price' => $this->input->post('price'),
                'currency' => 'SAR',
                'location_ar' => $this->input->post('location_ar'),
                'location_en' => $this->input->post('location_en'),
                'address_ar' => $this->input->post('address_ar'),
                'address_en' => $this->input->post('address_en'),
                'area' => $this->input->post('area') ? $this->input->post('area') : NULL,
                'bedrooms' => $this->input->post('bedrooms') ? $this->input->post('bedrooms') : NULL,
                'bathrooms' => $this->input->post('bathrooms') ? $this->input->post('bathrooms') : NULL,
                'features_ar' => $this->input->post('features_ar'),
                'features_en' => $this->input->post('features_en'),
                'image' => $main_image,
                'gallery' => !empty($gallery_images) ? json_encode($gallery_images) : NULL,
                'featured' => $this->input->post('featured') ? 1 : 0,
                'status' => 'active'
            );

            if ($this->add_property_to_db($property_data)) {
                $this->session->set_flashdata('success', 'Property added successfully with ' . (count($gallery_images) + ($main_image ? 1 : 0)) . ' images!');
                redirect('admin');
            } else {
                $this->session->set_flashdata('error', 'Failed to add property. Please try again.');
                redirect('realestate/add_real_estate_offer');
            }
        }
    }

    private function add_property_to_db($data)
    {
        return $this->db->insert('properties', $data);
    }

    private function get_type_arabic($type)
    {
        $types = array(
            'residential' => 'سكني',
            'commercial' => 'تجاري',
            'administrative' => 'إداري'
        );

        return isset($types[$type]) ? $types[$type] : $type;
    }

     
    public function edit_property($id = null)
    {
        // Get ID from parameter or URI segment
        if ($id === null) {
            $id = $this->uri->segment(2);
        }

        // Validate ID
        if (empty($id) || !is_numeric($id)) {
            $this->session->set_flashdata('error', 'Invalid offer ID provided.');
            redirect('realstate-offer-list');
            return;
        }

        $data['ln'] = $this->session->userdata('ln');
        $data['ln'] = !empty($data['ln']) ? $data['ln'] : 'ar';
        $data['menu'] = 'edit-realestate-offer';
        $data['id'] = $id;

        // Load the correct model - check if it exists first
        if (file_exists(APPPATH . 'models/Realestate_model1.php')) {
            $this->load->model('Realestate_model1', 'realestate');
            $data['offerData'] = $this->realestate->getOfferById($id);
        } else {
            // Fallback to regular model
            $data['offerData'] = $this->Realestate_model->get_property($id);
        }

        // Check if offer exists
        if (empty($data['offerData'])) {
            $this->session->set_flashdata('error', 'Offer not found.');
            redirect('realstate-offer-list');
            return;
        }

        $this->load->view('admin/header', $data);
        $this->load->view('realestate/add_real_estate_offer', $data);
        $this->load->view('admin/footer', $data);
    }


    public function delete_property($id)
    {
        $this->db->where('id', $id);
        if ($this->db->delete('properties')) {
            $this->session->set_flashdata('success', 'Property deleted successfully!');
        } else {
            $this->session->set_flashdata('error', 'Failed to delete property.');
        }
        redirect('realstate-offer-list');
    }

    public function toggle_featured($id)
    {
        $property = $this->Realestate_model->get_property($id);
        if ($property) {
            $new_featured = $property->featured ? 0 : 1;
            $this->db->where('id', $id);
            $this->db->update('properties', array('featured' => $new_featured));

            $status = $new_featured ? 'featured' : 'unfeatured';
            $this->session->set_flashdata('success', "Property $status successfully!");
        }
        redirect('realstate-offer-list');
    }

    private function upload_multiple_images()
    {
        $result = array(
            'main_image' => NULL,
            'gallery_images' => array()
        );

        // Create upload directory if it doesn't exist
        $upload_path = './assets/images/properties/';
        if (!is_dir($upload_path)) {
            mkdir($upload_path, 0755, true);
        }

        $config['upload_path'] = $upload_path;
        $config['allowed_types'] = 'gif|jpg|png|jpeg';
        $config['max_size'] = 2048; // 2MB
        $config['encrypt_name'] = TRUE;

        // Upload main image
        if (!empty($_FILES['main_image']['name'])) {
            $this->upload->initialize($config);
            if ($this->upload->do_upload('main_image')) {
                $upload_data = $this->upload->data();
                $result['main_image'] = $upload_data['file_name'];
            } else {
                $this->session->set_flashdata('error', 'Main image upload failed: ' . $this->upload->display_errors());
            }
        }

        // Upload gallery images
        if (!empty($_FILES['gallery_images']['name'][0])) {
            $files_count = count($_FILES['gallery_images']['name']);

            for ($i = 0; $i < $files_count; $i++) {
                if (!empty($_FILES['gallery_images']['name'][$i])) {
                    $_FILES['gallery_image']['name'] = $_FILES['gallery_images']['name'][$i];
                    $_FILES['gallery_image']['type'] = $_FILES['gallery_images']['type'][$i];
                    $_FILES['gallery_image']['tmp_name'] = $_FILES['gallery_images']['tmp_name'][$i];
                    $_FILES['gallery_image']['error'] = $_FILES['gallery_images']['error'][$i];
                    $_FILES['gallery_image']['size'] = $_FILES['gallery_images']['size'][$i];

                    $this->upload->initialize($config);
                    if ($this->upload->do_upload('gallery_image')) {
                        $upload_data = $this->upload->data();
                        $result['gallery_images'][] = $upload_data['file_name'];
                    } else {
                        $this->session->set_flashdata('error', 'Gallery image ' . ($i + 1) . ' upload failed: ' . $this->upload->display_errors());
                    }
                }
            }
        }

        return $result;
    }


}
