<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Realestate_model extends CI_Model {

    public function __construct()
    {
        parent::__construct();

        // Test database connection and table existence
        if (!$this->db->table_exists('properties')) {
            log_message('error', 'Properties table does not exist in database');
        }
    }

    public function get_properties($limit = 10, $offset = 0, $filters = array())
    {
        try {
            $this->db->select('*');
            $this->db->from('properties');
            $this->db->where('status', 'active');

            // Apply filters
            if (!empty($filters['type'])) {
                $this->db->where('type', $filters['type']);
            }

            if (!empty($filters['min_price'])) {
                $this->db->where('price >=', $filters['min_price']);
            }

            if (!empty($filters['max_price'])) {
                $this->db->where('price <=', $filters['max_price']);
            }

            if (!empty($filters['location'])) {
                $this->db->group_start();
                $this->db->like('location_ar', $filters['location']);
                $this->db->or_like('location_en', $filters['location']);
                $this->db->group_end();
            }

            $this->db->order_by('created_at', 'DESC');
            $this->db->limit($limit, $offset);

            $query = $this->db->get();

            if ($query === FALSE) {
                log_message('error', 'Database query failed in get_properties: ' . $this->db->error()['message']);
                return array();
            }

            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in get_properties: ' . $e->getMessage());
            return array();
        }
    }

    public function get_property($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status', 'active');

        $query = $this->db->get('properties');

        // Check if query was successful
        if ($query === FALSE) {
            log_message('error', 'Database query failed in get_property: ' . $this->db->error()['message']);
            return NULL; // Return NULL instead of causing fatal error
        }

        return $query->row();
    }

    public function get_featured_properties($limit = 6)
    {
        $this->db->where('featured', 1);
        $this->db->where('status', 'active');
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);

        $query = $this->db->get('properties');

        // Check if query was successful
        if ($query === FALSE) {
            log_message('error', 'Database query failed in get_featured_properties: ' . $this->db->error()['message']);
            return array(); // Return empty array instead of causing fatal error
        }

        return $query->result();
    }

    public function get_related_properties($exclude_id, $type, $limit = 4)
    {
        try {
            $this->db->where('id !=', $exclude_id);
            $this->db->where('type', $type);
            $this->db->where('status', 'active');
            $this->db->order_by('RAND()');
            $this->db->limit($limit);

            $query = $this->db->get('properties');

            if ($query === FALSE) {
                log_message('error', 'Database query failed in get_related_properties: ' . $this->db->error()['message']);
                return array();
            }

            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in get_related_properties: ' . $e->getMessage());
            return array();
        }
    }

    public function count_all_properties($filters = array())
    {
        try {
            $this->db->from('properties');
            $this->db->where('status', 'active');

            // Apply filters
            if (!empty($filters['type'])) {
                $this->db->where('type', $filters['type']);
            }

            if (!empty($filters['min_price'])) {
                $this->db->where('price >=', $filters['min_price']);
            }

            if (!empty($filters['max_price'])) {
                $this->db->where('price <=', $filters['max_price']);
            }

            if (!empty($filters['location'])) {
                $this->db->group_start();
                $this->db->like('location_ar', $filters['location']);
                $this->db->or_like('location_en', $filters['location']);
                $this->db->group_end();
            }

            $result = $this->db->count_all_results();

            // Check if result is valid
            if ($result === FALSE) {
                log_message('error', 'Database query failed in count_all_properties: ' . $this->db->error()['message']);
                return 0; // Return 0 instead of causing fatal error
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Exception in count_all_properties: ' . $e->getMessage());
            return 0;
        }
    }

    public function get_property_types()
    {
        try {
            $this->db->select('type');
            $this->db->distinct();
            $this->db->where('status', 'active');
            $query = $this->db->get('properties');

            if ($query === FALSE) {
                log_message('error', 'Database query failed in get_property_types: ' . $this->db->error()['message']);
                return array();
            }

            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in get_property_types: ' . $e->getMessage());
            return array();
        }
    }

    public function get_locations()
    {
        try {
            $this->db->select('location_ar, location_en');
            $this->db->distinct();
            $this->db->where('status', 'active');
            $query = $this->db->get('properties');

            if ($query === FALSE) {
                log_message('error', 'Database query failed in get_locations: ' . $this->db->error()['message']);
                return array();
            }

            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in get_locations: ' . $e->getMessage());
            return array();
        }
    }

    public function search_properties($keyword, $limit = 10)
    {
        try {
            $this->db->select('*');
            $this->db->from('properties');
            $this->db->where('status', 'active');
            $this->db->group_start();
            $this->db->like('title_ar', $keyword);
            $this->db->or_like('title_en', $keyword);
            $this->db->or_like('description_ar', $keyword);
            $this->db->or_like('description_en', $keyword);
            $this->db->or_like('location_ar', $keyword);
            $this->db->or_like('location_en', $keyword);
            $this->db->group_end();
            $this->db->order_by('created_at', 'DESC');
            $this->db->limit($limit);

            $query = $this->db->get();

            if ($query === FALSE) {
                log_message('error', 'Database query failed in search_properties: ' . $this->db->error()['message']);
                return array();
            }

            return $query->result();
        } catch (Exception $e) {
            log_message('error', 'Exception in search_properties: ' . $e->getMessage());
            return array();
        }
    }

    	public function getOfferById($id)
	{
		$this->db->select('*');
		$this->db->from('properties');
		$this->db->where('status', 'active');
		$this->db->where('id', $id);
		$query = $this->db->get();
		return $query->row();
	}
}
