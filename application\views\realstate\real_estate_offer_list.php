<!-- <PERSON> Header -->
 

<!-- Main Content -->
<section class="py-5">
    <div class="container">
        <!-- Flash Messages -->
        <?php if ($this->session->flashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($this->session->flashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $this->session->flashdata('error'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5><i class="fas fa-building"></i> Total Properties</h5>
                        <h2><?php echo count($properties); ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5><i class="fas fa-star"></i> Featured</h5>
                        <h2><?php echo count(array_filter($properties, function($p) { return $p->featured; })); ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h5><i class="fas fa-eye"></i> Total Views</h5>
                        <h2><?php echo array_sum(array_column($properties, 'views')); ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h5><i class="fas fa-check"></i> Active</h5>
                        <h2><?php echo count(array_filter($properties, function($p) { return $p->status === 'active'; })); ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Properties List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> Properties Management</h5>
                <a href="<?php echo base_url('admin/add_property'); ?>" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Add New Property
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($properties)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-5x text-muted mb-3"></i>
                        <h4>No Properties Found</h4>
                        <p class="text-muted">Start by adding your first property!</p>
                        <a href="<?php echo base_url('admin/add_property'); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Property
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Property</th>
                                    <th>Type</th>
                                    <th>Price</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Views</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($properties as $property): ?>
                                    <tr>
                                        <td><?php echo $property->id; ?></td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($property->title_en); ?></strong>
                                                <?php if ($property->featured): ?>
                                                    <span class="featured-badge">Featured</span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted"><?php echo htmlspecialchars($property->title_ar); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo ucfirst($property->type); ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($property->price); ?> SAR</strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($property->location_en); ?></td>
                                        <td>
                                            <span class="status-<?php echo $property->status; ?>">
                                                <i class="fas fa-circle"></i> <?php echo ucfirst($property->status); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $property->views; ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?php echo base_url('admin/edit_property/' . $property->id); ?>"
                                                   class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="<?php echo base_url('admin/toggle_featured/' . $property->id); ?>"
                                                   class="btn btn-outline-warning" title="Toggle Featured">
                                                    <i class="fas fa-star"></i>
                                                </a>
                                                <a href="<?php echo base_url('admin/delete_property/' . $property->id); ?>"
                                                   class="btn btn-outline-danger" title="Delete"
                                                   onclick="return confirm('Are you sure you want to delete this property?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
