<!-- <PERSON> Header -->
<section class="page-header bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-5 fw-bold mb-2">
                    <i class="fas fa-plus-circle me-3"></i>Add New Property
                </h1>
                <p class="lead mb-0">Add a new property to the Alyanabea Real Estate portfolio</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="<?php echo base_url('admin'); ?>" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-5">
    <div class="container">
        <!-- Flash Messages -->
        <?php if ($this->session->flashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($this->session->flashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $this->session->flashdata('error'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Validation Errors -->
        <?php if (validation_errors()): ?>
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Please fix the following errors:</h6>
                <?php echo validation_errors(); ?>
            </div>
        <?php endif; ?>

        <!-- Add Property Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-building me-2"></i>Property Information Form</h4>
                    </div>
                    <div class="card-body p-4">
                        <?php echo form_open_multipart('admin/add_property', array('class' => 'needs-validation', 'novalidate' => '')); ?>

                        <!-- Basic Information -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary fw-bold border-bottom border-primary pb-2">
                                    <i class="fas fa-info-circle me-2"></i>Basic Information
                                </h5>
                            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title_ar" class="form-label">Arabic Title <span class="required">*</span></label>
                        <input type="text" class="form-control" id="title_ar" name="title_ar"
                               value="<?php echo set_value('title_ar'); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="title_en" class="form-label">English Title <span class="required">*</span></label>
                        <input type="text" class="form-control" id="title_en" name="title_en"
                               value="<?php echo set_value('title_en'); ?>" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="description_ar" class="form-label">Arabic Description <span class="required">*</span></label>
                        <textarea class="form-control" id="description_ar" name="description_ar" rows="4" required><?php echo set_value('description_ar'); ?></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="description_en" class="form-label">English Description <span class="required">*</span></label>
                        <textarea class="form-control" id="description_en" name="description_en" rows="4" required><?php echo set_value('description_en'); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

                        <!-- Property Details -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary fw-bold border-bottom border-primary pb-2">
                                    <i class="fas fa-building me-2"></i>Property Details
                                </h5>
                            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="type" class="form-label">Property Type <span class="required">*</span></label>
                        <select class="form-select" id="type" name="type" required>
                            <option value="">Select Type</option>
                            <option value="residential" <?php echo set_select('type', 'residential'); ?>>Residential (سكني)</option>
                            <option value="commercial" <?php echo set_select('type', 'commercial'); ?>>Commercial (تجاري)</option>
                            <option value="administrative" <?php echo set_select('type', 'administrative'); ?>>Administrative (إداري)</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="price" class="form-label">Price (SAR) <span class="required">*</span></label>
                        <input type="number" class="form-control" id="price" name="price"
                               value="<?php echo set_value('price'); ?>" step="0.01" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="area" class="form-label">Area (m²)</label>
                        <input type="number" class="form-control" id="area" name="area"
                               value="<?php echo set_value('area'); ?>">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="bedrooms" class="form-label">Bedrooms</label>
                        <input type="number" class="form-control" id="bedrooms" name="bedrooms"
                               value="<?php echo set_value('bedrooms'); ?>" min="0">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="bathrooms" class="form-label">Bathrooms</label>
                        <input type="number" class="form-control" id="bathrooms" name="bathrooms"
                               value="<?php echo set_value('bathrooms'); ?>" min="0">
                    </div>
                </div>
            </div>
        </div>

                        <!-- Location -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary fw-bold border-bottom border-primary pb-2">
                                    <i class="fas fa-map-marker-alt me-2"></i>Location Information
                                </h5>
                            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="location_ar" class="form-label">Arabic Location <span class="required">*</span></label>
                        <input type="text" class="form-control" id="location_ar" name="location_ar"
                               value="<?php echo set_value('location_ar'); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="location_en" class="form-label">English Location <span class="required">*</span></label>
                        <input type="text" class="form-control" id="location_en" name="location_en"
                               value="<?php echo set_value('location_en'); ?>" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="address_ar" class="form-label">Arabic Address (Optional)</label>
                        <textarea class="form-control" id="address_ar" name="address_ar" rows="2"><?php echo set_value('address_ar'); ?></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="address_en" class="form-label">English Address (Optional)</label>
                        <textarea class="form-control" id="address_en" name="address_en" rows="2"><?php echo set_value('address_en'); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

                        <!-- Features -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary fw-bold border-bottom border-primary pb-2">
                                    <i class="fas fa-star me-2"></i>Property Features
                                </h5>
                            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="features_ar" class="form-label">Arabic Features</label>
                        <textarea class="form-control" id="features_ar" name="features_ar" rows="3"
                                  placeholder="مطبخ مجهز,موقف سيارة,مصعد,أمن 24 ساعة"><?php echo set_value('features_ar'); ?></textarea>
                        <small class="form-text text-muted">Separate features with commas</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="features_en" class="form-label">English Features</label>
                        <textarea class="form-control" id="features_en" name="features_en" rows="3"
                                  placeholder="Equipped Kitchen,Parking,Elevator,24h Security"><?php echo set_value('features_en'); ?></textarea>
                        <small class="form-text text-muted">Separate features with commas</small>
                    </div>
                </div>
            </div>
        </div>

                        <!-- Property Images -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary fw-bold border-bottom border-primary pb-2">
                                    <i class="fas fa-images me-2"></i>Property Images
                                </h5>
                            </div>

            <!-- Main Image -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="main_image" class="form-label">Main Property Image</label>
                        <input type="file" class="form-control" id="main_image" name="main_image"
                               accept="image/*">
                        <small class="form-text text-muted">This will be the primary image (Max: 2MB)</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Main Image Preview</label>
                        <div id="main-image-preview" class="border rounded p-3 text-center" style="min-height: 120px; background: #f8f9fa;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="text-muted mt-2">No main image selected</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Images -->
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">Gallery Images</label>
                        <input type="file" class="form-control" id="gallery_images" name="gallery_images[]"
                               accept="image/*" multiple>
                        <small class="form-text text-muted">Select multiple images for property gallery (Max: 2MB each)</small>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Tips:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Hold Ctrl/Cmd to select multiple images</li>
                            <li>Recommended: 3-8 gallery images</li>
                            <li>Images will be displayed in property gallery</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Gallery Preview</label>
                        <div id="gallery-preview" class="border rounded p-3" style="min-height: 120px; background: #f8f9fa;">
                            <div class="text-center">
                                <i class="fas fa-images fa-3x text-muted"></i>
                                <p class="text-muted mt-2">No gallery images selected</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                        <!-- Settings -->
                        <div class="form-section mb-5">
                            <div class="section-header mb-4">
                                <h5 class="text-primary fw-bold border-bottom border-primary pb-2">
                                    <i class="fas fa-cog me-2"></i>Property Settings
                                </h5>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                                       <?php echo set_checkbox('featured', '1'); ?>>
                                <label class="form-check-label fw-bold" for="featured">
                                    <i class="fas fa-star text-warning me-2"></i>Mark as Featured Property
                                </label>
                                <small class="form-text text-muted d-block">Featured properties appear on the homepage and get priority in listings</small>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="text-center py-4">
                            <button type="submit" class="btn btn-primary btn-lg px-5 me-3">
                                <i class="fas fa-save me-2"></i>Add Property
                            </button>
                            <a href="<?php echo base_url('admin'); ?>" class="btn btn-outline-secondary btn-lg px-5">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>

                        <?php echo form_close(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Custom JavaScript for Admin Form -->
<script>
        // Main image preview functionality
        document.getElementById('main_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('main-image-preview');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <img src="${e.target.result}" class="img-fluid rounded" style="max-height: 150px;">
                        <p class="text-success mt-2"><i class="fas fa-check"></i> Main image: ${file.name}</p>
                    `;
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = `
                    <i class="fas fa-image fa-3x text-muted"></i>
                    <p class="text-muted mt-2">No main image selected</p>
                `;
            }
        });

        // Gallery images preview functionality
        document.getElementById('gallery_images').addEventListener('change', function(e) {
            const files = e.target.files;
            const preview = document.getElementById('gallery-preview');

            if (files.length > 0) {
                let previewHTML = `
                    <div class="row g-2">
                        <div class="col-12">
                            <p class="text-success mb-2"><i class="fas fa-check"></i> ${files.length} gallery images selected</p>
                        </div>
                `;

                // Show first 4 images as thumbnails
                for (let i = 0; i < Math.min(files.length, 4); i++) {
                    const file = files[i];
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imgDiv = document.getElementById(`gallery-thumb-${i}`);
                        if (imgDiv) {
                            imgDiv.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded" style="max-height: 60px; object-fit: cover;">`;
                        }
                    };
                    reader.readAsDataURL(file);

                    previewHTML += `
                        <div class="col-3">
                            <div id="gallery-thumb-${i}" class="border rounded p-1 text-center" style="height: 70px; background: #fff;">
                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                            </div>
                            <small class="text-muted d-block text-truncate">${file.name}</small>
                        </div>
                    `;
                }

                if (files.length > 4) {
                    previewHTML += `
                        <div class="col-12">
                            <small class="text-muted">... and ${files.length - 4} more images</small>
                        </div>
                    `;
                }

                previewHTML += `</div>`;
                preview.innerHTML = previewHTML;
            } else {
                preview.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-images fa-3x text-muted"></i>
                        <p class="text-muted mt-2">No gallery images selected</p>
                    </div>
                `;
            }
        });

        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
    </script>
</body>
</html>
