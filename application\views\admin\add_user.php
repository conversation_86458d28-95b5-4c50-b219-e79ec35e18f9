<?php $sess = (object)($this->session->userdata); ?>
<!-- Load the user form theme CSS -->
<link href="<?php echo base_url('assets/css/user-form.css'); ?>" rel="stylesheet" />
<div class="page-wrapper">
  <!-- ============================================================== -->
  <!-- Bread crumb and right sidebar toggle -->
  <!-- ============================================================== -->
  <div class="page-breadcrumb">
    <div class="row">
      <div class="col-7 align-self-center">
        <h2 class="page-title text-truncate text-dark font-weight-medium mb-1 <?php echo ($ln == 'ar') ? 'text-right' : 'text-left'; ?>"><?php echo $this->lang->line('ADDADMINS'); ?></h2>
        <div class="d-flex align-items-center">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb m-0 p-0">
              <li class="breadcrumb-item"><a href="<?php echo base_url('dashboard'); ?>" class="text-muted"><?php echo $this->lang->line('DASHBOARD'); ?></a></li>
              <li class="breadcrumb-item text-muted active" aria-current="page"><?php echo $this->lang->line('USERS'); ?></li>
            </ol>
          </nav>
        </div>
      </div>
      <div class="col-5 align-self-center">
        <div class="customize-input float-<?php echo ($ln == 'ar') ? 'left' : 'right'; ?>">
          <a href="<?php echo base_url('users'); ?>" class="btn btn-secondary">
            <i data-feather="arrow-left" class="feather-icon"></i>
            <?php echo $this->lang->line('BACK_TO_LIST'); ?>
          </a>
        </div>
      </div>
    </div>
  </div>
  <!-- ============================================================== -->
  <!-- End Bread crumb and right sidebar toggle -->
  <!-- ============================================================== -->
  <!-- ============================================================== -->
  <!-- Container fluid  -->
  <!-- ============================================================== -->
  <div class="container-fluid">
    <!-- ============================================================== -->
    <!-- Start Page Content -->
    <!-- ============================================================== -->
    <!-- basic table -->
    <div class="row">
      <div class="col-12">
        <div class="card user-card">
          <div class="card-header">
            <h4 class="mb-0"><?php echo $this->lang->line('ADDADMINS'); ?></h4>
          </div>
          <div class="card-body">
            <?php if ($sess->ag_can_create == 1 && $sess->ag_can_update == 1 && $sess->ag_can_read == 1 && $sess->ag_can_delete == 1 && $sess->ag_view_stats == 1 ) : ?>
              <form id="frmUsers" class="user-form" action="<?php echo base_url('users') . "/saveuser"; ?>" method="post" enctype="multipart/form-data">
                <!-- Hidden fields -->
                <input type="hidden" id="task" name="task" value="<?php echo !isset($user) ? '1' : ''; ?>" />
                <input type="hidden" id="recid" name="recid" value="<?php echo isset($user) ? $user->ID : ''; ?>" />

                <!-- Status -->
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="form-group row">
                      <label class="col-sm-3 col-form-label">
                        <?php echo $this->lang->line('STATUS'); ?>
                        <span class="required-field">*</span>
                      </label>
                      <div class="col-sm-9">
                        <div class="radio-container">
                          <div class="radio-item">
                            <input type="radio"
                                   id="active"
                                   name="IsActive"
                                   value="1"
                                   <?php if (isset($user) && $user->IsActive == 1) echo "checked"; ?>
                                   required="required" />
                            <label for="active"><?php echo $this->lang->line('ACTIVE'); ?></label>
                          </div>
                          <div class="radio-item">
                            <input type="radio"
                                   id="inactive"
                                   name="IsActive"
                                   value="0"
                                   <?php if (isset($user) && $user->IsActive == 0) echo "checked"; ?>
                                   required="required" />
                            <label for="inactive"><?php echo $this->lang->line('INACTIVE'); ?></label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Name -->
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="form-group row">
                      <label class="col-sm-3 col-form-label">
                        <?php echo $this->lang->line('NAME'); ?>
                        <span class="required-field">*</span>
                      </label>
                      <div class="col-sm-9">
                        <input type="text"
                               id="firstname"
                               name="FirstName"
                               maxlength="100"
                               class="form-control"
                               autocomplete="off"
                               value="<?php echo isset($user) ? $user->FirstName : ''; ?>"
                               required="required" />
                      </div>
                    </div>
                  </div>

                  <!-- Email -->
                  <div class="col-md-6">
                    <div class="form-group row">
                      <label class="col-sm-3 col-form-label">
                        <?php echo $this->lang->line('email'); ?>
                        <span class="required-field">*</span>
                      </label>
                      <div class="col-sm-9">
                        <input type="email"
                               id="email"
                               name="Email"
                               maxlength="100"
                               class="form-control"
                               autocomplete="off"
                               value="<?php echo isset($user) ? $user->Email : ''; ?>"
                               required="required" />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Mobile & Admin Group -->
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="form-group row">
                      <label class="col-sm-3 col-form-label">
                        <?php echo $this->lang->line('MOBILE'); ?>
                        <span class="required-field">*</span>
                      </label>
                      <div class="col-sm-9">
                        <input type="number"
                               id="mobileNumber"
                               name="Mobile"
                               class="form-control"
                               autocomplete="off"
                               oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"
                               maxlength="12"
                               value="<?php echo isset($user) ? $user->Mobile : ''; ?>"
                               required="required" />
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="form-group row">
                      <label class="col-sm-3 col-form-label">
                        <?php echo $this->lang->line('ADMIN_GROUPS'); ?>
                        <span class="required-field">*</span>
                      </label>
                      <div class="col-sm-9">
                        <select class="form-select form-control" name="admin_group" id="admin_group">
                          <?php foreach ($admin_groups as $ak => $ag) {
                            $ag = (object) $ag;
                          ?>
                            <option value="<?php echo $ag->id; ?>"><?php echo $ag->name_en; ?></option>
                          <?php } ?>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Password -->
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="form-group row">
                      <label class="col-sm-3 col-form-label">
                        <?php echo $this->lang->line('password'); ?>
                        <span class="required-field">*</span>
                      </label>
                      <div class="col-sm-9">
                        <input type="text"
                               id="password"
                               name="Password"
                               maxlength="100"
                               class="form-control"
                               autocomplete="off"
                               value="<?php echo isset($user) ? $user->Password : ''; ?>"
                               required="required" />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="row mt-4">
                  <div class="col-12">
                    <button type="submit" id="cmdsubmit" class="btn btn-user btn-user-success">
                      <i class="fas fa-save"></i> <?php echo $this->lang->line('submit'); ?>
                    </button>
                    <button type="reset" class="btn btn-user btn-user-secondary mx-2">
                      <i class="fas fa-undo"></i> <?php echo $this->lang->line('reset'); ?>
                    </button>
                    <a href="<?php echo base_url('users'); ?>" class="btn btn-user btn-user-secondary">
                      <i class="fas fa-times"></i> <?php echo $this->lang->line('exit'); ?>
                    </a>
                  </div>
                </div>
              </form>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>


  </div>
  <!-- ============================================================== -->
  <!-- End Container fluid  -->
  <!-- ============================================================== -->
  <!-- ============================================================== -->
  <!-- footer -->
  <!-- ============================================================== -->
  <footer class="footer text-center text-muted">
    <!-- Footer content removed -->
  </footer>
  <!-- ============================================================== -->
  <!-- End footer -->
  <!-- ============================================================== -->
</div>


<!-- content-wrapper ends -->



<!-- Scripts -->
<script>
  $(document).ready(function(e) {
    // Form validation and submission
    var frm_validator;

    $("#frmUsers").submit(function(e) {
      frm_validator = $("#frmUsers").valid();
    });

    $('#frmUsers').ajaxForm({
      target: '',
      dataType: 'json',
      beforeSubmit: function() {
        if (frm_validator == true) {
          $("#cmdsubmit").html('<i class="fas fa-spinner fa-spin"></i> <?php echo $this->lang->line('loading'); ?>');
          $("#cmdsubmit").prop("disabled", true);
          return true;
        } else {
          return false;
        }
      },
      success: function(res_data) {
        $("#cmdsubmit").prop("disabled", false);
        $("#cmdsubmit").html('<i class="fas fa-save"></i> <?php echo $this->lang->line('submit'); ?>');

        if (res_data[0].resSuccess == 1) {
          showSuccessToast(res_data[0].msg, '<?php echo $this->lang->line('success'); ?>');
          setTimeout(function() {
            window.location.href = '<?php echo base_url('users'); ?>';
          }, 2000);
        } else {
          if (res_data[0].errtype == 'Validation') {
            showDangerToast(res_data[0].msg, '<?php echo $this->lang->line('danger'); ?>');
          } else {
            showDangerToast(res_data[0].msg, '<?php echo $this->lang->line('danger'); ?>');
          }
        }
      },
      error: function() {
        $("#cmdsubmit").prop("disabled", false);
        $("#cmdsubmit").html('<i class="fas fa-save"></i> <?php echo $this->lang->line('submit'); ?>');
        showDangerToast('<?php echo $this->lang->line('ERROR_OCCURRED'); ?>', '<?php echo $this->lang->line('danger'); ?>');
      }
    });

    // Custom validation messages for Arabic
    <?php if ($ln == 'ar') { ?>
      $.extend($.validator.messages, {
        required: "هذا الحقل إلزامي",
        remote: "يرجى تصحيح هذا الحقل للمتابعة",
        email: "رجاء إدخال عنوان بريد إلكتروني صحيح",
        url: "رجاء إدخال عنوان موقع إلكتروني صحيح",
        date: "رجاء إدخال تاريخ صحيح",
        dateISO: "رجاء إدخال تاريخ صحيح (ISO)",
        number: "رجاء إدخال عدد بطريقة صحيحة",
        digits: "رجاء إدخال أرقام فقط",
        creditcard: "رجاء إدخال رقم بطاقة ائتمان صحيح",
        equalTo: "رجاء إدخال نفس القيمة",
        extension: "رجاء إدخال ملف بامتداد موافق عليه",
        maxlength: $.validator.format("الحد الأقصى لعدد الحروف هو {0}"),
        minlength: $.validator.format("الحد الأدنى لعدد الحروف هو {0}"),
        rangelength: $.validator.format("عدد الحروف يجب أن يكون بين {0} و {1}"),
        range: $.validator.format("رجاء إدخال عدد قيمته بين {0} و {1}"),
        max: $.validator.format("رجاء إدخال عدد أقل من أو يساوي {0}"),
        min: $.validator.format("رجاء إدخال عدد أكبر من أو يساوي {0}")
      });
    <?php } ?>

    // Form validation configuration
    $("#frmUsers").validate({
      errorPlacement: function(label, element) {
        label.addClass('mt-2 text-danger <?php echo ($ln == 'ar') ? 'lbl-error' : ''; ?>');
        label.insertAfter(element);
      },
      highlight: function(element, errorClass) {
        $(element).addClass('is-invalid');
      },
      unhighlight: function(element, errorClass) {
        $(element).removeClass('is-invalid');
      }
    });

    // Flash message fade out
    $("#flasherr").fadeTo(2000, 500).slideUp(500, function() {
      $("#flasherr").slideUp(500);
    });

    // Custom radio button styling
    $(".radio-item input[type='radio']").on("change", function() {
      if ($(this).is(":checked")) {
        $(this).parent().siblings().find("input[type='radio']").prop("checked", false);
      }
    });
  });
</script>