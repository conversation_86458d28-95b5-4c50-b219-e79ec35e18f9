<?php $sess = (object)($this->session->userdata); ?>
<!-- Load the theme CSS for consistent styling -->
<link href="<?php echo base_url('assets/css/table-theme.css'); ?>" rel="stylesheet" />
<link href="<?php echo base_url('assets/css/realstate-offers.css'); ?>" rel="stylesheet" />
<?php include APPPATH . 'views/includes/theme-header.php'; ?>

<div class="page-wrapper">
  <!-- Bread crumb and right sidebar toggle -->
  <div class="page-breadcrumb">
    <div class="row">
      <div class="col-7 align-self-center">
        <h2 class="page-title text-truncate text-dark font-weight-medium mb-1 <?php echo ($ln == 'ar') ? 'text-right' : 'text-left'; ?>"><?php echo ($ln == 'ar') ? 'إدارة العروض العقارية' : 'Real Estate Offers Management'; ?></h2>
        <div class="d-flex align-items-center">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb m-0 p-0">
              <li class="breadcrumb-item"><a href="<?php echo base_url('dashboard'); ?>" class="text-muted"><?php echo ($ln == 'ar') ? 'لوحة التحكم' : 'Dashboard'; ?></a></li>
              <li class="breadcrumb-item text-muted active" aria-current="page"><?php echo ($ln == 'ar') ? 'العروض العقارية' : 'Real Estate Offers'; ?></li>
            </ol>
          </nav>
        </div>
      </div>
      <div class="col-5 align-self-center">
        <div class="customize-input float-end">
          <a href="<?php echo base_url('add-realestate-offer'); ?>" class="btn btn-success">
            <i class="fas fa-plus"></i> <?php echo ($ln == 'ar') ? 'إضافة عقار جديد' : 'Add New Property'; ?>
          </a>
        </div>
      </div>
    </div>
  </div>
  <!-- End Bread crumb and right sidebar toggle -->

  <!-- Container fluid -->
  <div class="container-fluid">
    <!-- Flash Messages -->
    <?php if ($this->session->flashdata('success')): ?>
      <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <?php if ($this->session->flashdata('error')): ?>
      <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle"></i> <?php echo $this->session->flashdata('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card offers-card" style="background: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-light)); color: white;">
          <div class="card-body text-center">
            <div class="d-flex align-items-center justify-content-center mb-2">
              <i class="fas fa-building fa-2x"></i>
            </div>
            <h3 class="mb-1"><?php echo count($properties); ?></h3>
            <p class="mb-0"><?php echo ($ln == 'ar') ? 'إجمالي العقارات' : 'Total Properties'; ?></p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card offers-card" style="background: linear-gradient(135deg, var(--theme-success), var(--theme-accent)); color: white;">
          <div class="card-body text-center">
            <div class="d-flex align-items-center justify-content-center mb-2">
              <i class="fas fa-star fa-2x"></i>
            </div>
            <h3 class="mb-1"><?php echo count(array_filter($properties, function($p) { return isset($p->featured) && $p->featured; })); ?></h3>
            <p class="mb-0"><?php echo ($ln == 'ar') ? 'العقارات المميزة' : 'Featured Properties'; ?></p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="card offers-card" style="background: linear-gradient(135deg, var(--theme-secondary), var(--theme-secondary-light)); color: white;">
          <div class="card-body text-center">
            <div class="d-flex align-items-center justify-content-center mb-2">
              <i class="fas fa-check-circle fa-2x"></i>
            </div>
            <h3 class="mb-1"><?php echo count(array_filter($properties, function($p) { return isset($p->status) && $p->status === 'active'; })); ?></h3>
            <p class="mb-0"><?php echo ($ln == 'ar') ? 'العقارات النشطة' : 'Active Properties'; ?></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Properties List -->
    <div class="row">
      <div class="col-12">
        <div class="card offers-card">
          <div class="card-header">
            <h4 class="card-title"><font color="white"><?php echo ($ln == 'ar') ? 'قائمة العقارات' : 'Advertisement Properties List'; ?></font></h4>
          </div>
          <div class="card-body">
            <?php if (empty($properties)): ?>
              <div class="text-center py-5">
                <i class="fas fa-building fa-5x text-muted mb-3"></i>
                <h4><?php echo ($ln == 'ar') ? 'لا توجد عقارات' : 'No Properties Found'; ?></h4>
                <p class="text-muted"><?php echo ($ln == 'ar') ? 'ابدأ بإضافة أول عقار!' : 'Start by adding your first property!'; ?></p>
                <a href="<?php echo base_url('add-realestate-offer'); ?>" class="btn btn-success">
                  <i class="fas fa-plus"></i> <?php echo ($ln == 'ar') ? 'إضافة عقار' : 'Add Property'; ?>
                </a>
              </div>
            <?php else: ?>
              <div class="table-responsive">
                <?php if ($sess->ag_can_create == 1 || $sess->ag_can_update == 1) : ?>
                  <table id="user-listing" class="table table-striped table-bordered">
                    <thead>
                      <tr>
                        <th><?php echo ($ln == 'ar') ? 'الرقم' : 'ID'; ?></th>
                        <th><?php echo ($ln == 'ar') ? 'العقار' : 'Property'; ?></th>
                        <th><?php echo ($ln == 'ar') ? 'النوع' : 'Type'; ?></th>
                        <th><?php echo ($ln == 'ar') ? 'السعر' : 'Price'; ?></th>
                        <th><?php echo ($ln == 'ar') ? 'الموقع' : 'Location'; ?></th>
                        <th><?php echo ($ln == 'ar') ? 'الحالة' : 'Status'; ?></th>                        
                        <th><?php echo ($ln == 'ar') ? 'الإجراءات' : 'Actions'; ?></th>
                      </tr>
                    </thead>
                    <tbody>
                      <?php foreach ($properties as $property): ?>
                        <tr>
                          <td><?php echo $property->id; ?></td>
                          <td>
                            <div>
                              <strong><?php echo htmlspecialchars($property->title_en); ?></strong>
                              <?php if (isset($property->featured) && $property->featured): ?>
                                <span class="badge bg-warning ms-2"><?php echo ($ln == 'ar') ? 'مميز' : 'Featured'; ?></span>
                              <?php endif; ?>
                            </div>
                            <small class="text-muted"><?php echo htmlspecialchars($property->title_ar); ?></small>
                          </td>
                          <td>
                            <span class="badge bg-info"><?php echo ucfirst($property->type); ?></span>
                          </td>
                          <td>
                            <strong><?php echo number_format($property->price); ?> <?php echo ($ln == 'ar') ? 'ريال' : 'SAR'; ?></strong>
                          </td>
                          <td>
                            <?php echo htmlspecialchars($property->location_en); ?><br>
                            <small class="text-muted"><?php echo htmlspecialchars($property->location_ar); ?></small>
                          </td>
                          <td>
                            <?php if (isset($property->status) && $property->status === 'active'): ?>
                              <span class="badge bg-success"><?php echo ($ln == 'ar') ? 'نشط' : 'Active'; ?></span>
                            <?php else: ?>
                              <span class="badge bg-secondary"><?php echo ($ln == 'ar') ? 'غير نشط' : 'Inactive'; ?></span>
                            <?php endif; ?>
                          </td>
                           
                          <td class="action-buttons">
                            <a href="<?php echo base_url('edit-realestate-offer/' . $property->id); ?>" title="<?php echo ($ln == 'ar') ? 'تعديل' : 'Edit'; ?>" class="btn btn-sm btn-primary">
                              <i data-feather="edit" class="feather-icon"></i>
                            </a>
                            <?php if (isset($property->featured) && $property->featured == 1): ?>
                              <a href="<?php echo base_url('RealEstateOffers/toggle_featured/' . $property->id); ?>" title="<?php echo ($ln == 'ar') ? 'إلغاء المميز' : 'Remove Featured'; ?>" class="btn btn-sm btn-warning">
                                <i data-feather="star" class="feather-icon" style="fill: currentColor;"></i>
                              </a>
                            <?php else: ?>
                              <a href="<?php echo base_url('RealEstateOffers/toggle_featured/' . $property->id); ?>" title="<?php echo ($ln == 'ar') ? 'جعل مميز' : 'Make Featured'; ?>" class="btn btn-sm btn-outline-warning">
                                <i data-feather="star" class="feather-icon"></i>
                              </a>
                            <?php endif; ?>
                            <a href="<?php echo base_url('RealEstateOffers/delete_property/' . $property->id); ?>" title="<?php echo ($ln == 'ar') ? 'حذف' : 'Delete'; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo ($ln == 'ar') ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?'; ?>')">
                              <i data-feather="trash-2" class="feather-icon"></i>
                            </a>
                          </td>
                        </tr>
                      <?php endforeach; ?>
                    </tbody>
                  </table>
                <?php endif; ?>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- End Container fluid -->
</div>
<!-- End Page wrapper -->

<style>
/* Center the pagination */
.dataTables_wrapper .dataTables_paginate {
    text-align: center !important;
    float: none !important;
    display: block !important;
    margin-top: 15px !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 0 2px !important;
}

/* Center the bottom controls */
.dataTables_wrapper .bottom {
    text-align: center !important;
}

.dataTables_wrapper .dataTables_info {
    float: none !important;
    text-align: center !important;
    margin-bottom: 10px !important;
}

/* RTL support for centered pagination */
<?php if ($ln == 'ar'): ?>
.dataTables_wrapper .dataTables_paginate {
    direction: ltr !important;
}
<?php endif; ?>
</style>

<script>
    (function($) {
        'use strict';
        $(function() {
            var table = $('#user-listing').DataTable({
                "aLengthMenu": [
                    [5, 10, 15, -1],
                    [5, 10, 15, "All"]
                ],
                "iDisplayLength": 10,
                "ordering": false,
                "language": {
                    <?php if ($ln == 'ar') { ?> "url": "<?php echo base_url('assets/vendors/datatables.net/Arabic.json'); ?>"
                    <?php } else { ?> "url": ""
                    <?php } ?>
                },
                "drawCallback": function() {
                    // Re-initialize feather icons after each draw
                    if (typeof feather !== 'undefined') {
                        feather.replace();
                    }
                }
            });

            // Add search placeholder
            setTimeout(function() {
                var search_input = $('.dataTables_filter input');
                search_input.attr('placeholder', '<?php echo ($ln == 'ar') ? 'البحث...' : 'Search...'; ?>');
            }, 100);
        });
    })(jQuery);

    // Delete property function
    function deleteProperty(id) {
        if (confirm('<?php echo ($ln == 'ar') ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?'; ?>')) {
            window.location.href = '<?php echo base_url('admin/delete_property/'); ?>' + id;
        }
    }
</script>
