<?php $sess = (object)($this->session->userdata); ?>

<div class="page-wrapper">
    <!-- Bread crumb and right sidebar toggle -->
    <div class="page-breadcrumb">
        <div class="row">
            <div class="col-7 align-self-center">
                <h2 class="page-title text-truncate text-dark font-weight-medium mb-1 <?php echo ($ln == 'ar') ? 'text-right' : 'text-left'; ?>"><?php echo $this->lang->line('REAL_ESTATE_OFFERS_LIST'); ?></h2>
                <div class="d-flex align-items-center">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb m-0 p-0">
                            <li class="breadcrumb-item"><a href="<?php echo base_url('dashboard'); ?>" class="text-muted"><?php echo $this->lang->line('DASHBOARD'); ?></a></li>
                            <li class="breadcrumb-item text-muted active" aria-current="page"><?php echo $this->lang->line('REAL_ESTATE_OFFERS_LIST'); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
            <div class="col-5 align-self-center">
                <div class="customize-input float-<?php echo ($ln == 'ar') ? 'left' : 'right'; ?>">
                    <?php if ($sess->ag_can_create == 1) : ?>
                        <a href="<?php echo base_url('add-realestate-offer'); ?>" class="add-button">
                            <i data-feather="file-plus" class="feather-icon"></i>
                            <?php echo $this->lang->line('add'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <!-- End Bread crumb and right sidebar toggle -->

    <!-- Container fluid -->
    <div class="container-fluid">
        <!-- Start Page Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title"><?php echo $this->lang->line('REAL_ESTATE_OFFERS_LIST'); ?></h4>
                    </div>
                    <div class="card-body">
                        <!-- Professional DataTables CSS -->
                        <style>
                            /* Prevent horizontal scroll */
                            body, html, .container-fluid, .page-wrapper, .card-body {
                                overflow-x: hidden !important;
                            }

                            /* Clean DataTables wrapper */
                            .dataTables_wrapper {
                                width: 100% !important;
                                margin: 0 !important;
                                padding: 0 !important;
                                overflow: hidden !important;
                            }

                            /* Top controls layout */
                            .dataTables_wrapper .top {
                                display: flex !important;
                                justify-content: space-between !important;
                                align-items: center !important;
                                margin-bottom: 15px !important;
                                flex-wrap: wrap !important;
                            }

                            .dataTables_wrapper .dataTables_length {
                                margin: 0 !important;
                                display: flex !important;
                                align-items: center !important;
                            }

                            .dataTables_wrapper .dataTables_length label {
                                margin: 0 !important;
                                display: flex !important;
                                align-items: center !important;
                                gap: 5px !important;
                            }

                            .dataTables_wrapper .dataTables_length select {
                                margin: 0 5px !important;
                                padding: 5px 10px !important;
                                border: 1px solid #ddd !important;
                                border-radius: 4px !important;
                                background: white !important;
                            }

                            .dataTables_wrapper .dataTables_filter {
                                margin: 0 !important;
                                display: flex !important;
                                align-items: center !important;
                            }

                            .dataTables_wrapper .dataTables_filter label {
                                margin: 0 !important;
                                display: flex !important;
                                align-items: center !important;
                                gap: 5px !important;
                            }

                            .dataTables_wrapper .dataTables_filter input {
                                margin: 0 !important;
                                padding: 5px 10px !important;
                                border: 1px solid #ddd !important;
                                border-radius: 4px !important;
                                width: 200px !important;
                            }

                            /* Table styling */
                            #user-listing {
                                width: 100% !important;
                                margin: 0 !important;
                                border-collapse: collapse !important;
                            }

                            #user-listing th,
                            #user-listing td {
                                padding: 8px 6px !important;
                                text-align: left !important;
                                border: 1px solid #ddd !important;
                                word-wrap: break-word !important;
                                overflow: hidden !important;
                                text-overflow: ellipsis !important;
                            }

                            #user-listing th {
                                background-color: #f8f9fa !important;
                                font-weight: 600 !important;
                            }

                            /* Bottom controls layout */
                            .dataTables_wrapper .bottom {
                                display: flex !important;
                                justify-content: space-between !important;
                                align-items: center !important;
                                margin-top: 15px !important;
                                flex-wrap: wrap !important;
                            }

                            .dataTables_wrapper .dataTables_info {
                                margin: 0 !important;
                                color: #666 !important;
                            }

                            /* Clean pagination */
                            .dataTables_wrapper .dataTables_paginate {
                                margin: 0 !important;
                                display: flex !important;
                                align-items: center !important;
                                gap: 5px !important;
                            }

                            .dataTables_wrapper .dataTables_paginate .paginate_button {
                                padding: 6px 12px !important;
                                margin: 0 !important;
                                border: 1px solid #ddd !important;
                                background: white !important;
                                color: #333 !important;
                                text-decoration: none !important;
                                border-radius: 4px !important;
                                cursor: pointer !important;
                                min-width: auto !important;
                                display: inline-block !important;
                            }

                            .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
                                background: #28a745 !important;
                                color: white !important;
                                border-color: #28a745 !important;
                            }

                            .dataTables_wrapper .dataTables_paginate .paginate_button.current {
                                background: #28a745 !important;
                                color: white !important;
                                border-color: #28a745 !important;
                            }

                            .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
                                background: #f8f9fa !important;
                                color: #6c757d !important;
                                border-color: #ddd !important;
                                cursor: not-allowed !important;
                            }

                            /* Mobile responsive */
                            @media (max-width: 768px) {
                                .dataTables_wrapper .top,
                                .dataTables_wrapper .bottom {
                                    flex-direction: column !important;
                                    gap: 10px !important;
                                    text-align: center !important;
                                }

                                .dataTables_wrapper .dataTables_filter input {
                                    width: 150px !important;
                                }

                                .dataTables_wrapper .dataTables_paginate .paginate_button {
                                    padding: 4px 8px !important;
                                    font-size: 12px !important;
                                }

                                #user-listing th,
                                #user-listing td {
                                    padding: 6px 4px !important;
                                    font-size: 12px !important;
                                }
                            }

                            @media (max-width: 480px) {
                                .dataTables_wrapper .dataTables_filter input {
                                    width: 120px !important;
                                }

                                .dataTables_wrapper .dataTables_paginate .paginate_button {
                                    padding: 3px 6px !important;
                                    font-size: 11px !important;
                                }

                                #user-listing th,
                                #user-listing td {
                                    padding: 4px 2px !important;
                                    font-size: 11px !important;
                                }
                            }
                        </style>

                        <div class="table-container">
                            <?php if ($sess->ag_can_create == 1 || $sess->ag_can_update == 1) : ?>
                                <table id="user-listing" class="table table-striped table-bordered">
                                    <thead>
                                        <tr>
                                            <th><?php echo $this->lang->line('ID'); ?></th>
                                            <th><?php echo $this->lang->line('PROPERTY_NAME'); ?></th>
                                            <th><?php echo $this->lang->line('TYPE'); ?></th>
                                            <th><?php echo $this->lang->line('PRICE'); ?></th>
                                            <th><?php echo $this->lang->line('LOCATION'); ?></th>
                                            <th><?php echo $this->lang->line('STATUS'); ?></th>
                                            <th><?php echo $this->lang->line('VIEWS'); ?></th>
                                            <th><?php echo $this->lang->line('DELEEDIT'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if (count($reslaestateofferslist) == 0) {
                                            // Show no data message
                                            echo '<tr><td colspan="8" class="text-center"><div class="alert alert-info">';
                                            echo '<i data-feather="info" class="feather-icon"></i> ';
                                            echo $this->lang->line('NO_DATA_AVAILABLE_IN_TABLE');
                                            echo '</div></td></tr>';
                                        } else {
                                            foreach ($reslaestateofferslist as $property) {
                                        ?>
                                            <tr>
                                                <td><?php echo $property->id; ?></td>
                                                <td style="min-width: 100px;">
                                                    <strong><?php echo ($ln == 'ar') ? htmlspecialchars($property->title) : htmlspecialchars($property->title_en ?? $property->title); ?></strong>
                                                    <?php if (isset($property->featured) && $property->featured): ?>
                                                        <br><span class="badge bg-warning text-dark"><?php echo $this->lang->line('FEATURED'); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td style="min-width: 50px;">
                                                    <span class="badge bg-secondary"><?php echo ucfirst($property->property_type ?? $property->type ?? 'N/A'); ?></span>
                                                </td>
                                                <td style="min-width: 50px;">
                                                    <strong><?php echo number_format($property->price ?? 0); ?> <?php echo $this->lang->line('SAR'); ?></strong>
                                                </td>
                                                <td style="min-width: 100px;"><?php echo htmlspecialchars($property->district ?? $property->location ?? 'N/A'); ?></td>
                                                <td style="min-width: 50px;">
                                                    <?php if ($property->is_active == '1'): ?>
                                                        <span class="badge bg-success"><?php echo $this->lang->line('ACTIVE'); ?></span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger"><?php echo $this->lang->line('INACTIVE'); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $property->views ?? 0; ?></td>
                                                <td class="action-buttons">
                                                    <?php if ($sess->ag_can_update == 1) : ?>
                                                        <button title="<?php echo $this->lang->line('EDIT'); ?>" onclick="window.location.href='<?php echo base_url('edit-realestate-offer/' . $property->id); ?>'" type="button" class="btn btn-sm btn-primary">
                                                            <i data-feather="edit" class="feather-icon"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($sess->ag_can_delete == 1) : ?>
                                                        <button title="<?php echo $this->lang->line('DELETE'); ?>" onclick="deleteProperty('<?php echo $property->id; ?>')" type="button" class="btn btn-sm btn-danger">
                                                            <i data-feather="trash-2" class="feather-icon"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php
                                            }
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DataTables JavaScript -->
<script>
$(document).ready(function() {
    var tableConfig = {
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "order": [[ 0, "desc" ]],
        "responsive": true,
        "dom": '<"top"lf>rt<"bottom"ip><"clear">',
        "pagingType": "simple_numbers"
    };

    <?php if ($ln == 'ar'): ?>
    tableConfig.language = {
        "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
    };
    <?php endif; ?>

    $('#user-listing').DataTable(tableConfig);
});

// Delete property function
function deleteProperty(propertyId) {
    if (confirm('<?php echo $this->lang->line('ARE_YOU_SURE'); ?>')) {
        window.location.href = '<?php echo base_url('delete-realestate-offer/'); ?>' + propertyId;
    }
}
</script>
