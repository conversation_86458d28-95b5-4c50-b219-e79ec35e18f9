<?php $sess = (object)($this->session->userdata); ?>
<!-- Load the user form theme CSS for consistent styling -->
<link href="<?php echo base_url('assets/css/user-form.css'); ?>" rel="stylesheet" />
<?php include APPPATH . 'views/includes/theme-header.php'; ?>

<div class="page-wrapper">
  <!-- Bread crumb and right sidebar toggle -->
  <div class="page-breadcrumb">
    <div class="row">
      <div class="col-7 align-self-center">
        <h2 class="page-title text-truncate text-dark font-weight-medium mb-1 <?php echo ($ln == 'ar') ? 'text-right' : 'text-left'; ?>"><?php echo ($ln == 'ar') ? 'العروض العقارية' : 'Real Estate Offers'; ?></h2>
        <div class="d-flex align-items-center">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb m-0 p-0">
              <li class="breadcrumb-item"><a href="<?php echo base_url('dashboard'); ?>" class="text-muted"><?php echo ($ln == 'ar') ? 'لوحة التحكم' : 'Dashboard'; ?></a></li>
              <li class="breadcrumb-item text-muted active" aria-current="page"><?php echo ($ln == 'ar') ? 'العروض العقارية' : 'Real Estate Offers'; ?></li>
            </ol>
          </nav>
        </div>
      </div>
      <div class="col-5 align-self-center">
        <div class="customize-input float-end">
          <a href="<?php echo base_url('add-realestate-offer'); ?>" class="btn btn-success">
            <i class="fas fa-plus"></i> <?php echo ($ln == 'ar') ? 'إضافة عرض جديد' : 'Add New Offer'; ?>
          </a>
        </div>
      </div>
    </div>
  </div>
  <!-- End Bread crumb and right sidebar toggle -->

  <!-- Container fluid -->
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="mb-0"><?php echo ($ln == 'ar') ? 'إدارة العروض العقارية' : 'Real Estate Offers Management'; ?></h4>
          </div>
          <div class="card-body">
        <!-- Flash Messages -->
        <?php if ($this->session->flashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($this->session->flashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $this->session->flashdata('error'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

            <!-- Statistics -->
            <div class="row mb-4">
              <div class="col-md-3">
                <div class="card bg-primary text-white">
                  <div class="card-body">
                    <h5><i class="fas fa-building"></i> <?php echo ($ln == 'ar') ? 'إجمالي العقارات' : 'Total Properties'; ?></h5>
                    <h2><?php echo isset($reslaestateofferslist) ? count($reslaestateofferslist) : 0; ?></h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card bg-success text-white">
                  <div class="card-body">
                    <h5><i class="fas fa-star"></i> <?php echo ($ln == 'ar') ? 'مميز' : 'Featured'; ?></h5>
                    <h2><?php echo isset($reslaestateofferslist) ? count(array_filter($reslaestateofferslist, function($p) { return isset($p->featured) && $p->featured; })) : 0; ?></h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card bg-info text-white">
                  <div class="card-body">
                    <h5><i class="fas fa-eye"></i> <?php echo ($ln == 'ar') ? 'إجمالي المشاهدات' : 'Total Views'; ?></h5>
                    <h2><?php echo isset($reslaestateofferslist) ? array_sum(array_column($reslaestateofferslist, 'views')) : 0; ?></h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card bg-warning text-white">
                  <div class="card-body">
                    <h5><i class="fas fa-check"></i> <?php echo ($ln == 'ar') ? 'نشط' : 'Active'; ?></h5>
                    <h2><?php echo isset($reslaestateofferslist) ? count(array_filter($reslaestateofferslist, function($p) { return isset($p->is_active) && $p->is_active == '1'; })) : 0; ?></h2>
                  </div>
                </div>
              </div>
            </div>

            <!-- Properties List -->
            <?php if (empty($reslaestateofferslist)): ?>
              <div class="text-center py-5">
                <i class="fas fa-building fa-5x text-muted mb-3"></i>
                <h4><?php echo ($ln == 'ar') ? 'لا توجد عقارات' : 'No Properties Found'; ?></h4>
                <p class="text-muted"><?php echo ($ln == 'ar') ? 'ابدأ بإضافة أول عقار!' : 'Start by adding your first property!'; ?></p>
                <a href="<?php echo base_url('add-realestate-offer'); ?>" class="btn btn-primary">
                  <i class="fas fa-plus"></i> <?php echo ($ln == 'ar') ? 'إضافة عقار' : 'Add Property'; ?>
                </a>
              </div>
            <?php else: ?>
              <div class="table-responsive">
                <table class="table table-hover" id="realEstateTable">
                  <thead class="table-dark">
                    <tr>
                      <th><?php echo ($ln == 'ar') ? 'الرقم' : 'ID'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'العقار' : 'Property'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'النوع' : 'Type'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'السعر' : 'Price'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'الموقع' : 'Location'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'الحالة' : 'Status'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'المشاهدات' : 'Views'; ?></th>
                      <th><?php echo ($ln == 'ar') ? 'الإجراءات' : 'Actions'; ?></th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($reslaestateofferslist as $property): ?>
                      <tr>
                        <td><?php echo $property->id; ?></td>
                        <td>
                          <div>
                            <strong><?php echo ($ln == 'ar') ? htmlspecialchars($property->title) : htmlspecialchars($property->title_en ?? $property->title); ?></strong>
                            <?php if (isset($property->featured) && $property->featured): ?>
                              <span class="badge bg-warning text-dark"><?php echo ($ln == 'ar') ? 'مميز' : 'Featured'; ?></span>
                            <?php endif; ?>
                          </div>
                          <small class="text-muted"><?php echo htmlspecialchars($property->title); ?></small>
                        </td>
                        <td>
                          <span class="badge bg-secondary"><?php echo ucfirst($property->property_type ?? $property->type ?? 'N/A'); ?></span>
                        </td>
                        <td>
                          <strong><?php echo number_format($property->price ?? 0); ?> <?php echo ($ln == 'ar') ? 'ريال' : 'SAR'; ?></strong>
                        </td>
                        <td><?php echo htmlspecialchars($property->district ?? $property->location ?? 'N/A'); ?></td>
                        <td>
                          <?php if ($property->is_active == '1'): ?>
                            <span class="badge bg-success"><?php echo ($ln == 'ar') ? 'نشط' : 'Active'; ?></span>
                          <?php else: ?>
                            <span class="badge bg-danger"><?php echo ($ln == 'ar') ? 'غير نشط' : 'Inactive'; ?></span>
                          <?php endif; ?>
                        </td>
                        <td><?php echo $property->views ?? 0; ?></td>
                        <td>
                          <div class="btn-group btn-group-sm">
                            <a href="<?php echo base_url('edit-realestate-offer/' . $property->id); ?>"
                               class="btn btn-outline-primary" title="<?php echo ($ln == 'ar') ? 'تعديل' : 'Edit'; ?>">
                              <i class="fas fa-edit"></i>
                            </a>
                            <a href="<?php echo base_url('delete-realestate-offer/' . $property->id); ?>"
                               class="btn btn-outline-danger" title="<?php echo ($ln == 'ar') ? 'حذف' : 'Delete'; ?>"
                               onclick="return confirm('<?php echo ($ln == 'ar') ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?'; ?>')">
                              <i class="fas fa-trash"></i>
                            </a>
                          </div>
                        </td>
                      </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- DataTables CSS and JS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    var tableConfig = {
        "pageLength": 10,
        "responsive": true,
        "order": [[ 0, "desc" ]]
    };

    <?php if ($ln == 'ar'): ?>
    tableConfig.language = {
        "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
    };
    <?php endif; ?>

    $('#realEstateTable').DataTable(tableConfig);
});
</script>
