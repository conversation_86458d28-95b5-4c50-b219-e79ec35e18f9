

<!-- Main Content -->
<section class="py-5">
    <div class="container">
        <!-- Flash Messages -->
        <?php if ($this->session->flashdata('success')): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> <?php echo $this->session->flashdata('success'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($this->session->flashdata('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle"></i> <?php echo $this->session->flashdata('error'); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Validation Errors -->
        <?php if (validation_errors()): ?>
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Please fix the following errors:</h6>
                <?php echo validation_errors(); ?>
            </div>
        <?php endif; ?>

        <!-- Edit Property Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-edit me-2"></i>Edit Property Information</h4>
                    </div>
                    <div class="card-body p-4">
                        <?php echo form_open_multipart('realstate/insertestate', array('class' => 'needs-validation', 'novalidate' => '')); ?>
                        <input type="hidden" name="action" value="Edit">
                        <input type="text" name="offerId" value="<?php echo isset($offerData->id) ? $offerData->id : ''; ?>">

                        <!-- Debug Information (Remove this after testing) -->
                        <div class="alert alert-info">
                            <h6>Debug Information:</h6>
                            <p><strong>Offer ID:</strong> <?php echo isset($id) ? $id : 'Not set'; ?></p>
                            <p><strong>Offer Data:</strong> <?php echo isset($offerData) ? 'Available' : 'Not available'; ?></p>
                            <?php if (isset($offerData)): ?>
                                <p><strong>Title AR:</strong> <?php echo isset($offerData->title_ar) ? $offerData->title_ar : 'Not set'; ?></p>
                                <p><strong>Title EN:</strong> <?php echo isset($offerData->title_en) ? $offerData->title_en : 'Not set'; ?></p>
                                <p><strong>Price:</strong> <?php echo isset($offerData->price) ? $offerData->price : 'Not set'; ?></p>
                                <p><strong>Type:</strong> <?php echo isset($offerData->type) ? $offerData->type : 'Not set'; ?></p>
                            <?php endif; ?>
                        </div>



                        <!-- Basic Information -->
                        <div class="form-section mb-4">
                            <h4><i class="fas fa-info-circle"></i> Basic Information</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title_ar" class="form-label">Arabic Title <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="title_ar" name="title_ar"
                                               value="<?php echo isset($offerData->title_ar) ? $offerData->title_ar : set_value('title_ar'); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title_en" class="form-label">English Title <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="title_en" name="title_en"
                                               value="<?php echo isset($offerData->title_en) ? $offerData->title_en : set_value('title_en'); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description_ar" class="form-label">Arabic Description <span class="required">*</span></label>
                                        <textarea class="form-control" id="description_ar" name="description_ar" rows="4" required><?php echo isset($offerData->description_ar) ? $offerData->description_ar : set_value('description_ar'); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description_en" class="form-label">English Description <span class="required">*</span></label>
                                        <textarea class="form-control" id="description_en" name="description_en" rows="4" required><?php echo isset($offerData->description_en) ? $offerData->description_en : set_value('description_en'); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Property Details -->
                        <div class="form-section mb-4">
                            <h4><i class="fas fa-building"></i> Property Details</h4>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="type" class="form-label">Property Type <span class="required">*</span></label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="">Select Type</option>
                                            <option value="residential" <?php echo (isset($offerData->type) && $offerData->type == 'residential') ? 'selected' : ''; ?>>Residential (سكني)</option>
                                            <option value="commercial" <?php echo (isset($offerData->type) && $offerData->type == 'commercial') ? 'selected' : ''; ?>>Commercial (تجاري)</option>
                                            <option value="administrative" <?php echo (isset($offerData->type) && $offerData->type == 'administrative') ? 'selected' : ''; ?>>Administrative (إداري)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="price" class="form-label">Price (SAR) <span class="required">*</span></label>
                                        <input type="number" class="form-control" id="price" name="price"
                                               value="<?php echo isset($offerData->price) ? $offerData->price : set_value('price'); ?>" step="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="area" class="form-label">Area (m²)</label>
                                        <input type="number" class="form-control" id="area" name="area"
                                               value="<?php echo isset($offerData->area) ? $offerData->area : set_value('area'); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bedrooms" class="form-label">Bedrooms</label>
                                        <input type="number" class="form-control" id="bedrooms" name="bedrooms"
                                               value="<?php echo isset($offerData->bedrooms) ? $offerData->bedrooms : set_value('bedrooms'); ?>" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bathrooms" class="form-label">Bathrooms</label>
                                        <input type="number" class="form-control" id="bathrooms" name="bathrooms"
                                               value="<?php echo isset($offerData->bathrooms) ? $offerData->bathrooms : set_value('bathrooms'); ?>" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

        <!-- Location -->
        <div class="form-section">
            <h4><i class="fas fa-map-marker-alt"></i> Location</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="location_ar" class="form-label">Arabic Location <span class="required">*</span></label>
                        <input type="text" class="form-control" id="location_ar" name="location_ar"
                               value="<?php echo isset($offerData->location_ar) ? $offerData->location_ar : set_value('location_ar'); ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="location_en" class="form-label">English Location <span class="required">*</span></label>
                        <input type="text" class="form-control" id="location_en" name="location_en"
                               value="<?php echo isset($offerData->location_en) ? $offerData->location_en : set_value('location_en'); ?>" required>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="form-section">
            <h4><i class="fas fa-star"></i> Features</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="features_ar" class="form-label">Arabic Features</label>
                        <textarea class="form-control" id="features_ar" name="features_ar" rows="3"><?php echo isset($offerData->features_ar) ? $offerData->features_ar : set_value('features_ar'); ?></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="features_en" class="form-label">English Features</label>
                        <textarea class="form-control" id="features_en" name="features_en" rows="3"><?php echo isset($offerData->features_en) ? $offerData->features_en : set_value('features_en'); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Images -->
        <div class="form-section">
            <h4><i class="fas fa-images"></i> Property Images</h4>

            <!-- Main Image -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="main_image" class="form-label">Update Main Image</label>
                        <input type="file" class="form-control" id="main_image" name="main_image" accept="image/*">
                        <small class="form-text text-muted">Leave empty to keep current main image</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Current Main Image</label>
                        <div class="border rounded p-3">
                            <?php if (!empty($offerData->main_image) && file_exists('./uploads/properties/' . $offerData->main_image)): ?>
                                <img src="<?php echo base_url('uploads/properties/' . $offerData->main_image); ?>"
                                     class="current-image img-fluid rounded" alt="Current property image">
                                <p class="text-success mt-2"><i class="fas fa-check"></i> Current main image</p>
                            <?php else: ?>
                                <div class="text-center">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                    <p class="text-muted mt-2">No main image uploaded</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gallery Images -->
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">Add Gallery Images</label>
                        <input type="file" class="form-control" id="gallery_images" name="gallery_images[]"
                               accept="image/*" multiple>
                        <small class="form-text text-muted">Select multiple images to add to gallery</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="replace_gallery" name="replace_gallery" value="1">
                        <label class="form-check-label" for="replace_gallery">
                            Replace all existing gallery images
                        </label>
                        <small class="form-text text-muted d-block">Check this to replace all current gallery images with new ones</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Current Gallery Images</label>
                        <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            <?php
                            $gallery_images = !empty($offerData->gallery_images) ? json_decode($offerData->gallery_images, true) : array();
                            if (!empty($gallery_images)): ?>
                                <div class="row g-2">
                                    <?php foreach ($gallery_images as $index => $gallery_image): ?>
                                        <?php if (file_exists('./uploads/properties/' . $gallery_image)): ?>
                                            <div class="col-6">
                                                <div class="position-relative">
                                                    <img src="<?php echo base_url('uploads/properties/' . $gallery_image); ?>"
                                                         class="img-fluid rounded" style="height: 80px; object-fit: cover; width: 100%;"
                                                         alt="Gallery image <?php echo $index + 1; ?>">
                                                    <small class="text-muted d-block text-truncate"><?php echo $gallery_image; ?></small>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                                <p class="text-success mt-2"><i class="fas fa-check"></i> <?php echo count($gallery_images); ?> gallery images</p>
                            <?php else: ?>
                                <div class="text-center">
                                    <i class="fas fa-images fa-3x text-muted"></i>
                                    <p class="text-muted mt-2">No gallery images</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="form-section">
            <h4><i class="fas fa-cog"></i> Settings</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1"
                               <?php echo (isset($offerData->featured) && $offerData->featured) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="featured">
                            <i class="fas fa-star text-warning"></i> Mark as Featured Property
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" <?php echo (isset($offerData->status) && $offerData->status == 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="sold" <?php echo (isset($offerData->status) && $offerData->status == 'sold') ? 'selected' : ''; ?>>Sold</option>
                            <option value="rented" <?php echo (isset($offerData->status) && $offerData->status == 'rented') ? 'selected' : ''; ?>>Rented</option>
                            <option value="inactive" <?php echo (isset($offerData->status) && $offerData->status == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="text-center mb-4">
            <button type="submit" class="btn btn-success btn-lg me-3">
                <i class="fas fa-save"></i> Update Property
            </button>
            <a href="<?php echo base_url('admin'); ?>" class="btn btn-secondary btn-lg">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>

        <?php echo form_close(); ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Main image preview functionality
        document.getElementById('main_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const currentImageDiv = document.querySelector('.row.mb-4 .col-md-6:last-child .border');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentImageDiv.innerHTML = `
                        <img src="${e.target.result}" class="current-image img-fluid rounded" alt="New main image">
                        <p class="text-info mt-2"><i class="fas fa-upload"></i> New main image: ${file.name}</p>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });

        // Gallery images preview functionality
        document.getElementById('gallery_images').addEventListener('change', function(e) {
            const files = e.target.files;

            if (files.length > 0) {
                // Create preview section
                let existingPreview = document.getElementById('new-gallery-preview');
                if (!existingPreview) {
                    const gallerySection = document.querySelector('.row:last-child .col-md-6:first-child');
                    const previewDiv = document.createElement('div');
                    previewDiv.id = 'new-gallery-preview';
                    previewDiv.className = 'mt-3 p-3 border rounded bg-light';
                    gallerySection.appendChild(previewDiv);
                }

                const preview = document.getElementById('new-gallery-preview');
                let previewHTML = `
                    <h6><i class="fas fa-plus"></i> New Gallery Images (${files.length})</h6>
                    <div class="row g-2">
                `;

                // Show first 6 images as thumbnails
                for (let i = 0; i < Math.min(files.length, 6); i++) {
                    const file = files[i];
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const imgDiv = document.getElementById(`new-gallery-thumb-${i}`);
                        if (imgDiv) {
                            imgDiv.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded" style="height: 60px; object-fit: cover; width: 100%;">`;
                        }
                    };
                    reader.readAsDataURL(file);

                    previewHTML += `
                        <div class="col-4">
                            <div id="new-gallery-thumb-${i}" class="border rounded p-1 text-center" style="height: 70px; background: #fff;">
                                <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                            </div>
                            <small class="text-muted d-block text-truncate">${file.name}</small>
                        </div>
                    `;
                }

                if (files.length > 6) {
                    previewHTML += `
                        <div class="col-12">
                            <small class="text-muted">... and ${files.length - 6} more images</small>
                        </div>
                    `;
                }

                previewHTML += `</div>`;
                preview.innerHTML = previewHTML;
            }
        });

        // Replace gallery checkbox functionality
        document.getElementById('replace_gallery').addEventListener('change', function(e) {
            const galleryInput = document.getElementById('gallery_images');
            const label = document.querySelector('label[for="gallery_images"]');

            if (e.target.checked) {
                label.textContent = 'Replace All Gallery Images';
                galleryInput.nextElementSibling.textContent = 'These images will replace all existing gallery images';
            } else {
                label.textContent = 'Add Gallery Images';
                galleryInput.nextElementSibling.textContent = 'Select multiple images to add to gallery';
            }
        });
    </script>
</body>
</html>
