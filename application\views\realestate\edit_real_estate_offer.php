
<?php $sess = (object)($this->session->userdata); ?>
<!-- Load the user form theme CSS for consistent styling -->
<link href="<?php echo base_url('assets/css/user-form.css'); ?>" rel="stylesheet" />
<?php include APPPATH . 'views/includes/theme-header.php'; ?>

<div class="page-wrapper">
  <!-- Bread crumb and right sidebar toggle -->
  <div class="page-breadcrumb">
    <div class="row">
      <div class="col-7 align-self-center">
        <h2 class="page-title text-truncate text-dark font-weight-medium mb-1 <?php echo ($ln == 'ar') ? 'text-right' : 'text-left'; ?>"><?php echo ($ln == 'ar') ? 'تعديل العرض العقاري' : 'Edit Real Estate Offer'; ?></h2>
        <div class="d-flex align-items-center">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb m-0 p-0">
              <li class="breadcrumb-item"><a href="<?php echo base_url('dashboard'); ?>" class="text-muted"><?php echo ($ln == 'ar') ? 'لوحة التحكم' : 'Dashboard'; ?></a></li>
              <li class="breadcrumb-item"><a href="<?php echo base_url('realestate'); ?>" class="text-muted"><?php echo ($ln == 'ar') ? 'العروض العقارية' : 'Real Estate Offers'; ?></a></li>
              <li class="breadcrumb-item text-muted active" aria-current="page"><?php echo ($ln == 'ar') ? 'تعديل العرض العقاري' : 'Edit Real Estate Offer'; ?></li>
            </ol>
          </nav>
        </div>
      </div>
    </div>
  </div>
  <!-- End Bread crumb and right sidebar toggle -->

  <!-- Container fluid -->
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h4 class="mb-0"><?php echo ($ln == 'ar') ? 'تعديل العرض العقاري' : 'Edit Real Estate Offer'; ?></h4>
          </div>
          <div class="card-body">
            <!-- Toast Messages (JavaScript will handle display) -->
            <?php if ($this->session->flashdata('success')): ?>
              <script>
                $(document).ready(function() {
                  showSuccessToast('<?php echo addslashes($this->session->flashdata('success')); ?>');
                });
              </script>
            <?php endif; ?>

            <?php if ($this->session->flashdata('error')): ?>
              <script>
                $(document).ready(function() {
                  showDangerToast('<?php echo addslashes($this->session->flashdata('error')); ?>');
                });
              </script>
            <?php endif; ?>

            <!-- Validation Errors -->
            <?php if (validation_errors()): ?>
              <script>
                $(document).ready(function() {
                  showDangerToast('<?php echo ($ln == 'ar') ? 'يرجى إصلاح الأخطاء التالية' : 'Please fix the following errors'; ?>: <?php echo addslashes(strip_tags(validation_errors())); ?>');
                });
              </script>
            <?php endif; ?>

            <?php if ($sess->ag_can_update == 1) : ?>
              <form id="frmRealEstate" class="user-form" action="<?php echo base_url('realestate/update_property'); ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="property_id" value="<?php echo isset($offerData->id) ? $offerData->id : ''; ?>">

                <!-- Basic Information -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'العنوان بالعربية' : 'Arabic Title'; ?> <span class="required-field">*</span></label>
                      <input type="text" class="form-control" id="title_ar" name="title_ar" value="<?php echo isset($offerData->title_ar) ? htmlspecialchars($offerData->title_ar) : set_value('title_ar'); ?>" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'العنوان بالإنجليزية' : 'English Title'; ?> <span class="required-field">*</span></label>
                      <input type="text" class="form-control" id="title_en" name="title_en" value="<?php echo isset($offerData->title_en) ? htmlspecialchars($offerData->title_en) : set_value('title_en'); ?>" required>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'الوصف بالعربية' : 'Arabic Description'; ?> <span class="required-field">*</span></label>
                      <textarea class="form-control" id="description_ar" name="description_ar" rows="4" required><?php echo isset($offerData->description_ar) ? htmlspecialchars($offerData->description_ar) : set_value('description_ar'); ?></textarea>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'الوصف بالإنجليزية' : 'English Description'; ?> <span class="required-field">*</span></label>
                      <textarea class="form-control" id="description_en" name="description_en" rows="4" required><?php echo isset($offerData->description_en) ? htmlspecialchars($offerData->description_en) : set_value('description_en'); ?></textarea>
                    </div>
                  </div>
                </div>

                <!-- Property Details -->
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'نوع العقار' : 'Property Type'; ?> <span class="required-field">*</span></label>
                      <select class="form-control" id="type" name="type" required>
                        <option value=""><?php echo ($ln == 'ar') ? 'اختر النوع' : 'Select Type'; ?></option>
                        <option value="residential" <?php echo (isset($offerData->type) && $offerData->type == 'residential') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'سكني' : 'Residential'; ?></option>
                        <option value="commercial" <?php echo (isset($offerData->type) && $offerData->type == 'commercial') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'تجاري' : 'Commercial'; ?></option>
                        <option value="administrative" <?php echo (isset($offerData->type) && $offerData->type == 'administrative') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'إداري' : 'Administrative'; ?></option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'السعر (ريال سعودي)' : 'Price (SAR)'; ?> <span class="required-field">*</span></label>
                      <input type="number" class="form-control" id="price" name="price" value="<?php echo isset($offerData->price) ? $offerData->price : set_value('price'); ?>" step="0.01" required>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'المساحة (متر مربع)' : 'Area (m²)'; ?></label>
                      <input type="number" class="form-control" id="area" name="area" value="<?php echo isset($offerData->area) ? $offerData->area : set_value('area'); ?>">
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'غرف النوم' : 'Bedrooms'; ?></label>
                      <input type="number" class="form-control" id="bedrooms" name="bedrooms" value="<?php echo isset($offerData->bedrooms) ? $offerData->bedrooms : set_value('bedrooms'); ?>" min="0">
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'دورات المياه' : 'Bathrooms'; ?></label>
                      <input type="number" class="form-control" id="bathrooms" name="bathrooms" value="<?php echo isset($offerData->bathrooms) ? $offerData->bathrooms : set_value('bathrooms'); ?>" min="0">
                    </div>
                  </div>
                </div>

                <!-- Location -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'الموقع بالعربية' : 'Arabic Location'; ?> <span class="required-field">*</span></label>
                      <input type="text" class="form-control" id="location_ar" name="location_ar" value="<?php echo isset($offerData->location_ar) ? htmlspecialchars($offerData->location_ar) : set_value('location_ar'); ?>" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'الموقع بالإنجليزية' : 'English Location'; ?> <span class="required-field">*</span></label>
                      <input type="text" class="form-control" id="location_en" name="location_en" value="<?php echo isset($offerData->location_en) ? htmlspecialchars($offerData->location_en) : set_value('location_en'); ?>" required>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'العنوان بالعربية' : 'Arabic Address'; ?></label>
                      <textarea class="form-control" id="address_ar" name="address_ar" rows="2"><?php echo isset($offerData->address_ar) ? htmlspecialchars($offerData->address_ar) : set_value('address_ar'); ?></textarea>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'العنوان بالإنجليزية' : 'English Address'; ?></label>
                      <textarea class="form-control" id="address_en" name="address_en" rows="2"><?php echo isset($offerData->address_en) ? htmlspecialchars($offerData->address_en) : set_value('address_en'); ?></textarea>
                    </div>
                  </div>
                </div>

                <!-- Features -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'المميزات بالعربية' : 'Arabic Features'; ?></label>
                      <textarea class="form-control" id="features_ar" name="features_ar" rows="3" placeholder="مطبخ مجهز,موقف سيارة,مصعد,أمن 24 ساعة"><?php echo isset($offerData->features_ar) ? htmlspecialchars($offerData->features_ar) : set_value('features_ar'); ?></textarea>
                      <small class="form-text text-muted"><?php echo ($ln == 'ar') ? 'افصل بين المميزات بفاصلة' : 'Separate features with commas'; ?></small>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'المميزات بالإنجليزية' : 'English Features'; ?></label>
                      <textarea class="form-control" id="features_en" name="features_en" rows="3" placeholder="Equipped Kitchen,Parking,Elevator,24h Security"><?php echo isset($offerData->features_en) ? htmlspecialchars($offerData->features_en) : set_value('features_en'); ?></textarea>
                      <small class="form-text text-muted"><?php echo ($ln == 'ar') ? 'افصل بين المميزات بفاصلة' : 'Separate features with commas'; ?></small>
                    </div>
                  </div>
                </div>

                <!-- Property Images -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'تحديث الصورة الرئيسية' : 'Update Main Image'; ?></label>
                      <input type="file" class="form-control" id="main_image" name="main_image" accept="image/*">
                      <small class="form-text text-muted"><?php echo ($ln == 'ar') ? 'الحد الأقصى 2 ميجابايت' : 'Max 2MB'; ?></small>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'صور المعرض' : 'Gallery Images'; ?></label>
                      <input type="file" class="form-control" id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                      <small class="form-text text-muted"><?php echo ($ln == 'ar') ? 'اختر عدة صور' : 'Select multiple images'; ?></small>
                    </div>
                  </div>
                </div>

                <!-- Settings -->
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1" <?php echo (isset($offerData->featured) && $offerData->featured) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="featured">
                          <?php echo ($ln == 'ar') ? 'تمييز كعقار مميز' : 'Mark as Featured Property'; ?>
                        </label>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="col-form-label"><?php echo ($ln == 'ar') ? 'الحالة' : 'Status'; ?></label>
                      <select class="form-control" id="status" name="status">
                        <option value="active" <?php echo (isset($offerData->status) && $offerData->status == 'active') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'نشط' : 'Active'; ?></option>
                        <option value="sold" <?php echo (isset($offerData->status) && $offerData->status == 'sold') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'مباع' : 'Sold'; ?></option>
                        <option value="rented" <?php echo (isset($offerData->status) && $offerData->status == 'rented') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'مؤجر' : 'Rented'; ?></option>
                        <option value="inactive" <?php echo (isset($offerData->status) && $offerData->status == 'inactive') ? 'selected' : ''; ?>><?php echo ($ln == 'ar') ? 'غير نشط' : 'Inactive'; ?></option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="row mt-4">
                  <div class="col-12">
                    <button type="submit" id="cmdsubmit" class="btn btn-user btn-user-success">
                      <i class="fas fa-save"></i> <?php echo ($ln == 'ar') ? 'تحديث' : 'Update'; ?>
                    </button>
                    <button type="reset" class="btn btn-user btn-user-secondary mx-2">
                      <i class="fas fa-undo"></i> <?php echo ($ln == 'ar') ? 'إعادة تعيين' : 'Reset'; ?>
                    </button>
                    <a href="<?php echo base_url('realestate'); ?>" class="btn btn-user btn-user-secondary">
                      <i class="fas fa-times"></i> <?php echo ($ln == 'ar') ? 'خروج' : 'Exit'; ?>
                    </a>
                  </div>
                </div>
              </form>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- End Container fluid -->
</div>
<!-- End Page wrapper -->
